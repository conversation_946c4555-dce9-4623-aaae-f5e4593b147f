import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/server'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const postId = params.id

    // Check if user already loved this post
    const { data: existingLove } = await supabase
      .from('audio_loves')
      .select('id')
      .eq('user_id', user.id)
      .eq('audio_post_id', postId)
      .single()

    if (existingLove) {
      // Remove love
      const { error } = await supabase
        .from('audio_loves')
        .delete()
        .eq('user_id', user.id)
        .eq('audio_post_id', postId)

      if (error) {
        console.error('Error removing love:', error)
        return NextResponse.json({ error: 'Failed to remove love' }, { status: 500 })
      }

      return NextResponse.json({ loved: false })
    } else {
      // Add love
      const { error } = await supabase
        .from('audio_loves')
        .insert({
          user_id: user.id,
          audio_post_id: postId
        })

      if (error) {
        console.error('Error adding love:', error)
        return NextResponse.json({ error: 'Failed to add love' }, { status: 500 })
      }

      return NextResponse.json({ loved: true })
    }
  } catch (error) {
    console.error('Audio love API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createSupabaseServerClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ loved: false })
    }

    const postId = params.id

    // Check if user loved this post
    const { data: love } = await supabase
      .from('audio_loves')
      .select('id')
      .eq('user_id', user.id)
      .eq('audio_post_id', postId)
      .single()

    return NextResponse.json({ loved: !!love })
  } catch (error) {
    console.error('Audio love status API error:', error)
    return NextResponse.json({ loved: false })
  }
}
