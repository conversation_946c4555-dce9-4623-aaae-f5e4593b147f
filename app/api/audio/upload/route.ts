import { NextRequest, NextResponse } from 'next/server'
import { PutObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { r2Client, AUDIO_BUCKET, generateAudioKey, getAudioUrl } from '@/lib/cloudflare-r2'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    // Verify user authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { type, duration } = await request.json()
    
    // Validate input
    if (!type || !['post', 'reply'].includes(type)) {
      return NextResponse.json({ error: 'Invalid type' }, { status: 400 })
    }
    
    if (!duration || duration > 9.0) {
      return NextResponse.json({ error: 'Duration must be 9 seconds or less' }, { status: 400 })
    }

    // Generate unique key for the audio file
    const key = generateAudioKey(user.id, type)
    
    // Create presigned URL for upload
    const command = new PutObjectCommand({
      Bucket: AUDIO_BUCKET,
      Key: key,
      ContentType: 'audio/webm',
      Metadata: {
        userId: user.id,
        type: type,
        duration: duration.toString(),
        uploadedAt: new Date().toISOString(),
      },
    })

    const uploadUrl = await getSignedUrl(r2Client, command, { expiresIn: 3600 }) // 1 hour
    const publicUrl = getAudioUrl(key)
    
    return NextResponse.json({
      uploadUrl,
      key,
      publicUrl
    })
  } catch (error) {
    console.error('Audio upload error:', error)
    return NextResponse.json({ error: 'Upload failed' }, { status: 500 })
  }
}
