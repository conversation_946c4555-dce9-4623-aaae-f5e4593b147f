import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Get current user for follow status
    const { data: { user } } = await supabase.auth.getUser()

    // Fetch diary entries
    const { data: diaryEntries, error: diaryError } = await supabase
      .from('diary_entries')
      .select(`
        id,
        title,
        body_md,
        created_at,
        is_free,
        bundle_count,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url
        ),
        loves_count,
        comments_count,
        has_access,
        credits_required,
        photos (
          id,
          url,
          alt_text
        ),
        videos (
          id,
          r2_public_url,
          title,
          view_count,
          custom_thumbnail_url
        )
      `)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (diaryError) {
      console.error('Error fetching diary entries:', diaryError)
    }

    // Fetch audio posts
    const { data: audioPosts, error: audioError } = await supabase
      .from('audio_posts')
      .select(`
        id,
        audio_url,
        description,
        duration_seconds,
        love_count,
        reply_count,
        created_at,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url
        )
      `)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (audioError) {
      console.error('Error fetching audio posts:', audioError)
    }

    // Get follow status if user is logged in
    let followingIds: Set<string> = new Set()
    if (user) {
      const { data: follows } = await supabase
        .from('follows')
        .select('writer_id')
        .eq('follower_id', user.id)
      
      followingIds = new Set(follows?.map(f => f.writer_id) || [])
    }

    // Combine and format posts
    const combinedPosts = [
      ...(diaryEntries || []).map(entry => ({
        ...entry,
        type: 'diary' as const,
        isFollowing: user ? followingIds.has(entry.user.id) : false
      })),
      ...(audioPosts || []).map(post => ({
        ...post,
        type: 'audio' as const,
        isFollowing: user ? followingIds.has(post.user.id) : false
      }))
    ]

    // Sort by created_at (most recent first)
    combinedPosts.sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )

    // Apply offset and limit to combined results
    const paginatedPosts = combinedPosts.slice(offset, offset + limit)

    return NextResponse.json({ 
      posts: paginatedPosts,
      hasMore: combinedPosts.length > offset + limit
    })
  } catch (error) {
    console.error('Timeline API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
