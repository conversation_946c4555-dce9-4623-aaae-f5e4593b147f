import { S3Client } from '@aws-sdk/client-s3'

// Cloudflare R2 client configuration
export const r2Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT!,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
})

export const AUDIO_BUCKET = process.env.CLOUDFLARE_R2_BUCKET_NAME!

// Generate unique audio file key
export function generateAudioKey(userId: string, type: 'post' | 'reply'): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  return `audio-${type}s/${userId}/${timestamp}-${random}.webm`
}

// Get public URL for audio file
export function getAudioUrl(key: string): string {
  // For now, we'll use the R2 endpoint directly
  // Later you can set up a custom domain like audio.onlydiary.app
  return `${process.env.CLOUDFLARE_R2_ENDPOINT}/${process.env.CLOUDFLARE_R2_BUCKET_NAME}/${key}`
}
