import { S3Client } from '@aws-sdk/client-s3'

// Cloudflare R2 client configuration
console.log('R2 Config:', {
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
  hasAccessKey: !!process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
  hasSecretKey: !!process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
})

export const r2Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT!,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
})

export const AUDIO_BUCKET = process.env.CLOUDFLARE_R2_BUCKET_NAME!

// Generate unique audio file key
export function generateAudioKey(userId: string, type: 'post' | 'reply'): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  return `audio-${type}s/${userId}/${timestamp}-${random}.webm`
}

// Get public URL for audio file
export function getAudioUrl(key: string): string {
  // Use the public R2 URL if available, otherwise fall back to direct endpoint
  const publicUrl = process.env.CLOUDFLARE_R2_PUBLIC_URL
  if (publicUrl) {
    return `${publicUrl}/${key}`
  }

  // Fallback to direct endpoint (won't work without public access)
  return `${process.env.CLOUDFLARE_R2_ENDPOINT}/${process.env.CLOUDFLARE_R2_BUCKET_NAME}/${key}`
}
