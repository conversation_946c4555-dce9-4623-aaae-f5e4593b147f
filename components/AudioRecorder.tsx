'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'

interface AudioRecorderProps {
  onRecordingComplete: (audioBlob: Blob, duration: number) => void
  onCancel?: () => void
  maxDuration?: number
  className?: string
}

export function AudioRecorder({
  onRecordingComplete,
  onCancel,
  maxDuration = 9,
  className = ''
}: AudioRecorderProps) {
  const [isRecording, setIsRecording] = useState(false)
  const [duration, setDuration] = useState(0)
  const [audioLevel, setAudioLevel] = useState(0)
  const [permissionStatus, setPermissionStatus] = useState<'ready' | 'requesting' | 'denied'>('ready')
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioContextRef = useRef<AudioContext | null>(null)
  const analyserRef = useRef<AnalyserNode | null>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const chunksRef = useRef<Blob[]>([])
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const animationRef = useRef<number | null>(null)

  // Don't check permissions on mount - wait for user to click

  // Clean up on unmount
  useEffect(() => {
    return () => {
      stopRecording()
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop())
      }
      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
    }
  }, [])

  const requestMicrophonePermission = async () => {
    setPermissionStatus('requesting')

    try {
      // Request microphone access - this will trigger the browser popup
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        }
      })

      streamRef.current = stream
      setPermissionStatus('ready') // Reset to ready state after success
      return stream
    } catch (error: any) {
      console.error('Microphone permission error:', error)
      setPermissionStatus('denied')
      return null
    }
  }

  const startRecording = async () => {
    let stream = streamRef.current

    if (!stream) {
      stream = await requestMicrophonePermission()
      if (!stream) return
    }

    try {

      // Set up audio context for visualization
      audioContextRef.current = new AudioContext()
      analyserRef.current = audioContextRef.current.createAnalyser()
      const source = audioContextRef.current.createMediaStreamSource(stream)
      source.connect(analyserRef.current)
      analyserRef.current.fftSize = 256

      // Set up MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      })
      
      mediaRecorderRef.current = mediaRecorder
      chunksRef.current = []

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data)
        }
      }

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(chunksRef.current, { type: 'audio/webm' })
        onRecordingComplete(audioBlob, duration)
        cleanup()
      }

      // Start recording
      mediaRecorder.start()
      setIsRecording(true)
      setDuration(0)

      // Start duration timer
      intervalRef.current = setInterval(() => {
        setDuration(prev => {
          const newDuration = prev + 0.1
          if (newDuration >= maxDuration) {
            stopRecording()
            return maxDuration
          }
          return newDuration
        })
      }, 100)

      // Start audio level animation
      updateAudioLevel()

    } catch (error) {
      console.error('Error starting recording:', error)
      setPermissionStatus('denied')
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
    }
    cleanup()
  }

  const cleanup = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current)
      animationRef.current = null
    }
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
  }

  const updateAudioLevel = () => {
    if (!analyserRef.current) return

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount)
    analyserRef.current.getByteFrequencyData(dataArray)
    
    const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length
    setAudioLevel(average / 255) // Normalize to 0-1

    if (isRecording) {
      animationRef.current = requestAnimationFrame(updateAudioLevel)
    }
  }

  const handleCancel = () => {
    if (isRecording) {
      stopRecording()
    }
    setDuration(0)
    setAudioLevel(0)
    onCancel?.()
  }

  return (
    <div className={`bg-white rounded-2xl p-6 shadow-lg border border-gray-200 ${className}`}>
      <div className="text-center">
        <h3 className="text-lg font-serif text-gray-800 mb-4">
          Record Audio ({maxDuration}s max)
        </h3>
        
        {/* Visual feedback */}
        <div className="mb-6">
          <div className="flex justify-center items-center gap-1 mb-4 h-16">
            {/* Simple bar visualization */}
            {Array.from({ length: 20 }).map((_, i) => (
              <div
                key={i}
                className="w-2 bg-blue-500 rounded-full transition-all duration-100"
                style={{
                  height: isRecording 
                    ? `${Math.max(4, audioLevel * 60 + Math.random() * 20)}px`
                    : '4px',
                  opacity: isRecording ? 0.7 + audioLevel * 0.3 : 0.3
                }}
              />
            ))}
          </div>
          
          {/* Duration display */}
          <div className="text-2xl font-mono text-gray-700">
            {duration.toFixed(1)}s
          </div>
          
          {/* Progress bar */}
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-100"
              style={{ width: `${(duration / maxDuration) * 100}%` }}
            />
          </div>
        </div>

        {/* Controls */}
        <div className="flex gap-3 justify-center">
          {permissionStatus === 'requesting' ? (
            <Button
              disabled
              className="bg-gray-400 text-white px-8 py-3 rounded-full cursor-not-allowed"
            >
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Requesting Permission...
              </div>
            </Button>
          ) : permissionStatus === 'denied' ? (
            <div className="text-center">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <p className="text-red-700 font-medium mb-2">🎤 Microphone Access Required</p>
                <p className="text-red-600 text-sm mb-3">
                  To record audio, please allow microphone access when your browser asks.
                </p>
                <div className="text-xs text-red-500 bg-red-100 rounded p-2">
                  <strong>Chrome:</strong> Look for the microphone icon in your address bar and click "Allow"<br/>
                  <strong>Firefox:</strong> Click "Allow" when the permission popup appears<br/>
                  <strong>Safari:</strong> Go to Settings → Websites → Microphone and allow this site
                </div>
              </div>
              <Button
                onClick={() => {
                  setPermissionStatus('ready')
                  startRecording()
                }}
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-full mr-3"
              >
                🔄 Try Again
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                className="px-6 py-3 rounded-full"
              >
                Cancel
              </Button>
            </div>
          ) : !isRecording ? (
            <>
              <Button
                onClick={startRecording}
                className="bg-red-500 hover:bg-red-600 text-white px-8 py-3 rounded-full"
              >
                🎤 Start Recording
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                className="px-6 py-3 rounded-full"
              >
                Cancel
              </Button>
            </>
          ) : (
            <>
              <Button
                onClick={stopRecording}
                className="bg-green-500 hover:bg-green-600 text-white px-8 py-3 rounded-full"
              >
                ⏹️ Stop Recording
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                className="px-6 py-3 rounded-full"
              >
                Cancel
              </Button>
            </>
          )}
        </div>
        
        {isRecording && (
          <p className="text-sm text-gray-500 mt-3 text-center">
            Recording will automatically stop at {maxDuration} seconds
          </p>
        )}

        {permissionStatus === 'ready' && !isRecording && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-3">
            <p className="text-blue-700 text-sm text-center">
              🎤 Click "Start Recording" and allow microphone access when your browser asks
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
