'use client'

import { useState } from 'react'
import Image from 'next/image'
import { AudioPlayer } from './AudioPlayer'
import { AudioRecorder } from './AudioRecorder'
import { Heart, MessageCircle, User } from 'lucide-react'

interface AudioReply {
  id: string
  user_id: string
  audio_url: string
  duration_seconds: number
  love_count: number
  created_at: string
  user?: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
  }
}

interface AudioReplyProps {
  reply: AudioReply
  currentUserId?: string
  onLove?: (replyId: string) => void
  onReply?: (replyId: string) => void
  level?: number // For nested replies
}

export function AudioReply({ 
  reply, 
  currentUserId, 
  onLove, 
  onReply,
  level = 0 
}: AudioReplyProps) {
  const [isLoved, setIsLoved] = useState(false)
  const [loveCount, setLoveCount] = useState(reply.love_count)
  const [showReplyRecorder, setShowReplyRecorder] = useState(false)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
      return diffInMinutes < 1 ? 'Just now' : `${diffInMinutes}m ago`
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  const handleLove = async () => {
    if (!currentUserId || !onLove) return
    
    try {
      setIsLoved(!isLoved)
      setLoveCount(prev => isLoved ? prev - 1 : prev + 1)
      onLove(reply.id)
    } catch (error) {
      // Revert on error
      setIsLoved(isLoved)
      setLoveCount(reply.love_count)
      console.error('Error loving reply:', error)
    }
  }

  const handleReply = () => {
    setShowReplyRecorder(true)
  }

  const handleAudioReplyComplete = async (audioBlob: Blob, duration: number) => {
    try {
      // TODO: Implement nested audio reply functionality
      console.log('Nested audio reply recorded:', { duration, size: audioBlob.size })
      setShowReplyRecorder(false)
      alert('Nested audio replies coming soon!')
    } catch (error) {
      console.error('Error posting nested audio reply:', error)
      alert('Failed to post audio reply. Please try again.')
    }
  }

  const maxNestingLevel = 3 // Limit nesting depth

  return (
    <div className={`${level > 0 ? 'ml-4 sm:ml-8 mt-3' : 'mt-4'} ${level > 0 ? 'border-l-2 border-gray-100 pl-2 sm:pl-4' : ''}`}>
      <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
        {/* User info */}
        <div className="flex items-center gap-2 sm:gap-3 mb-3">
          <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0">
            {reply.user?.avatar || reply.user?.profile_picture_url ? (
              <Image
                src={(reply.user.avatar || reply.user.profile_picture_url) as string}
                alt={reply.user.name || 'User avatar'}
                width={32}
                height={32}
                className="w-full h-full object-cover"
              />
            ) : (
              <span className="text-xs font-serif text-gray-500">
                {reply.user?.name?.charAt(0).toUpperCase() || '?'}
              </span>
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-1 sm:gap-2">
              <span className="font-medium text-gray-900 text-xs sm:text-sm truncate">
                {reply.user?.name || 'Unknown User'}
              </span>
              <span className="text-gray-400 text-xs">•</span>
              <span className="text-gray-500 text-xs">
                {formatDate(reply.created_at)}
              </span>
            </div>
          </div>
        </div>

        {/* Audio player */}
        <div className="mb-3">
          <AudioPlayer
            audioUrl={reply.audio_url}
            duration={reply.duration_seconds}
            className="w-full max-w-full sm:max-w-sm"
          />
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2 sm:gap-4">
          <button
            onClick={handleLove}
            disabled={!currentUserId}
            className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-lg transition-all min-h-[44px] ${
              isLoved
                ? 'text-red-600 bg-red-50'
                : 'text-gray-600 hover:bg-gray-100'
            } ${!currentUserId ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <Heart className={`w-4 h-4 ${isLoved ? 'fill-current' : ''}`} />
            <span className="text-xs sm:text-sm font-medium">{loveCount}</span>
          </button>

          {level < maxNestingLevel && (
            <button
              onClick={handleReply}
              disabled={!currentUserId}
              className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-lg transition-all text-blue-600 hover:bg-blue-50 min-h-[44px] ${
                !currentUserId ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <div className="flex items-center gap-1">
                <span className="text-xs sm:text-sm">🎤</span>
                <MessageCircle className="w-4 h-4" />
              </div>
              <span className="text-xs sm:text-sm font-medium">Reply</span>
            </button>
          )}
        </div>

        {/* Nested reply recorder */}
        {showReplyRecorder && (
          <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium text-blue-900">🎤 Record Audio Reply</h4>
              <button
                onClick={() => setShowReplyRecorder(false)}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                Cancel
              </button>
            </div>
            <AudioRecorder
              maxDuration={9}
              onRecordingComplete={handleAudioReplyComplete}
              onCancel={() => setShowReplyRecorder(false)}
            />
          </div>
        )}
      </div>
    </div>
  )
}
