'use client'

import React, { useState, useEffect } from 'react'
import { AudioPost } from './AudioPost'
import { PaywallContent } from './PaywallContent'
import Image from 'next/image'
import Link from 'next/link'

interface DiaryEntry {
  id: string
  title: string
  body_md: string
  created_at: string
  is_free: boolean
  bundle_count: number
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
  }
  love_count: number
  view_count: number
  photos?: Array<{ id: string; url: string; alt_text: string }>
  type: 'diary'
  isFollowing: boolean
}

interface AudioPostType {
  id: string
  audio_url: string
  description?: string
  duration_seconds: number
  love_count: number
  reply_count: number
  created_at: string
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
  }
  type: 'audio'
  isFollowing: boolean
}

type TimelinePost = DiaryEntry | AudioPostType

interface UnifiedTimelineProps {
  currentUserId?: string
  onUserClick?: (userId: string) => void
}

export function UnifiedTimeline({ currentUserId, onUserClick }: UnifiedTimelineProps) {
  const [posts, setPosts] = useState<TimelinePost[]>([])
  const [loading, setLoading] = useState(true)
  const [hasMore, setHasMore] = useState(true)

  useEffect(() => {
    loadPosts()
  }, [])

  const loadPosts = async (offset = 0) => {
    try {
      const response = await fetch(`/api/timeline?limit=20&offset=${offset}`)
      const data = await response.json()
      
      if (offset === 0) {
        setPosts(data.posts || [])
      } else {
        setPosts(prev => [...prev, ...(data.posts || [])])
      }
      
      setHasMore(data.hasMore || false)
    } catch (error) {
      console.error('Error loading timeline:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAudioLove = async (postId: string) => {
    try {
      const response = await fetch(`/api/audio/posts/${postId}/love`, {
        method: 'POST'
      })
      
      if (response.ok) {
        const { loved } = await response.json()

        setPosts(prev => prev.map(post => {
          if (post.type === 'audio' && post.id === postId) {
            return {
              ...post,
              love_count: loved ? post.love_count + 1 : post.love_count - 1
            }
          }
          return post
        }))
      }
    } catch (error) {
      console.error('Error toggling audio love:', error)
    }
  }

  const handleAudioReply = (postId: string) => {
    // TODO: Implement audio reply functionality
    console.log('Audio reply for post:', postId)
  }

  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const postDate = new Date(dateString)
    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'now'
    if (diffInMinutes < 60) return `${diffInMinutes}m`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`
    return `${Math.floor(diffInMinutes / 1440)}d`
  }

  if (loading) {
    return (
      <div className="space-y-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 animate-pulse">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-20"></div>
              </div>
            </div>
            <div className="h-20 bg-gray-200 rounded mb-4"></div>
            <div className="flex gap-4">
              <div className="h-8 bg-gray-200 rounded w-16"></div>
              <div className="h-8 bg-gray-200 rounded w-16"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <>
      <div className="space-y-5">
        {posts.map((post) => {
        if (post.type === 'audio') {
          return (
            <AudioPost
              key={`audio-${post.id}`}
              post={post}
              currentUserId={currentUserId}
              isFollowing={post.isFollowing}

              onLove={handleAudioLove}
              onReply={handleAudioReply}
              onUserClick={onUserClick}
            />
          )
        } else {
          // Diary entry
          return (
            <div
              key={`diary-${post.id}`}
              className="rounded-2xl bg-white/90 dark:bg-zinc-900/80 shadow-sm drop-shadow-[0_3px_6px_rgba(0,0,0,0.06)] px-4 py-3 mb-5 sm:px-6 sm:py-4 transition-transform duration-200 hover:-translate-y-0.5"
            >
              {/* Header */}
              <div className="flex items-start gap-3 mb-2">
                <button
                  onClick={() => onUserClick?.(post.user.id)}
                  className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-100 via-pink-50 to-blue-100 flex items-center justify-center overflow-hidden flex-shrink-0 hover:ring-2 hover:ring-purple-200/50 transition-all cursor-pointer shadow-sm hover:shadow-md hover:scale-105 duration-200"
                >
                  {post.user.avatar || post.user.profile_picture_url ? (
                    <Image
                      src={(post.user.avatar || post.user.profile_picture_url) as string}
                      alt={post.user.name || 'User avatar'}
                      width={48}
                      height={48}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <span className="text-gray-500 text-lg">👤</span>
                  )}
                </button>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <button
                      onClick={() => onUserClick?.(post.user.id)}
                      className="font-bold text-gray-900 hover:text-transparent hover:bg-gradient-to-r hover:from-purple-600 hover:to-pink-600 hover:bg-clip-text transition-all duration-200 truncate text-sm"
                    >
                      {post.user.name}
                    </button>
                    <span className="text-gray-300 text-sm">•</span>
                    <span className="text-gray-400 text-xs font-medium">
                      {formatTimeAgo(post.created_at)}
                    </span>
                  </div>

                  <div className="flex items-center gap-2 text-xs text-gray-600 mb-2">
                    <div className="flex items-center gap-1 bg-purple-50 px-2 py-0.5 rounded-full">
                      <span className="text-purple-600 text-xs">📔</span>
                      <span className="font-medium text-purple-700 uppercase tracking-wide text-[10px]">DIARY ENTRY</span>
                    </div>
                    {post.isFollowing && (
                      <div className="flex items-center gap-1 bg-green-50 px-2 py-0.5 rounded-full">
                        <span className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></span>
                        <span className="text-green-700 uppercase tracking-wide text-[10px] font-medium">FOLLOWING</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Title */}
              <h2 className="text-lg font-serif text-gray-900 mb-3 leading-tight">
                {post.title}
              </h2>

              {/* Content */}
              <PaywallContent
                content={post.body_md}
                hasAccess={post.has_access}
                isLoggedIn={!!currentUserId}
                entryId={post.id}
                isFree={post.is_free}
                creditsRequired={post.credits_required}
                bundleCount={post.bundle_count}
                photos={post.photos}
                writerName={post.user.name}
                writerId={post.user.id}
              />

              {/* Actions - Floating naturally */}
              <div className="flex items-center justify-between mt-6 px-4 sm:px-6">
                <div className="flex items-center gap-6">
                  <button className="group flex items-center gap-3 text-gray-600 hover:text-red-500 transition-all duration-300 hover:scale-105">
                    <div className="relative">
                      <span className="text-xl group-hover:animate-pulse">❤️</span>
                      <div className="absolute -inset-2 bg-red-100/50 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 -z-10 blur-sm"></div>
                    </div>
                    <span className="text-sm font-bold bg-gradient-to-r from-red-500 to-pink-500 bg-clip-text text-transparent group-hover:from-red-600 group-hover:to-pink-600">{post.love_count}</span>
                  </button>
                  <div className="flex items-center gap-3 text-gray-500 hover:text-gray-700 transition-colors duration-300">
                    <span className="text-xl">👁️</span>
                    <span className="text-sm font-medium">{post.view_count}</span>
                  </div>
                </div>

                {/* Subtle floating indicator */}
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse"></div>
                  <div className="w-1 h-1 bg-pink-300 rounded-full animate-pulse delay-100"></div>
                  <div className="w-1.5 h-1.5 bg-blue-300 rounded-full animate-pulse delay-200"></div>
                </div>
              </div>
            </div>
          )
        }
      })}

      {hasMore && (
        <div className="text-center py-8">
          <button
            onClick={() => loadPosts(posts.length)}
            className="px-6 py-3 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium text-gray-700 transition-colors"
          >
            Load More
          </button>
        </div>
      )}

      {posts.length === 0 && !loading && (
        <div className="text-center py-12">
          <p className="text-gray-500 font-serif text-lg mb-4">
            No posts yet. Start creating content!
          </p>
          <Link
            href="/write"
            className="inline-block px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
          >
            Create Your First Post
          </Link>
        </div>
      )}
      </div>
    </>
  )
}
