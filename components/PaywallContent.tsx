"use client"

import { useState } from "react"
import Image from "next/image"
import { processPaywallContent } from "@/lib/paywall"

interface Photo {
  id: string
  url: string
  alt_text: string
}

interface PaywallContentProps {
  content: string
  photos?: Photo[]
  isFree: boolean
  hasAccess: boolean
  writerName: string
  writerId: string
  entryId?: string
  projectId?: string
  type?: 'diary' | 'chapter'
}

export function PaywallContent({
  content,
  photos = [],
  isFree,
  hasAccess,
  writerName,
  writerId,
  entryId,
  projectId,
  type = 'diary'
}: PaywallContentProps) {
  const [selectedPhoto, setSelectedPhoto] = useState<string | null>(null)
  const [subscribing, setSubscribing] = useState(false)
  const [showFullContent, setShowFullContent] = useState(false)

  const handleSubscribe = async () => {
    if (subscribing) return

    setSubscribing(true)
    try {
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ writerId }),
      })
      const data = await response.json()
      if (data.url) {
        window.location.href = data.url
      } else {
        alert(data.error || 'Failed to create subscription')
        setSubscribing(false)
      }
    } catch {
      alert('Failed to create subscription')
      setSubscribing(false)
    }
  }

  const { truncatedContent, remainingWords, needsSubscription } = processPaywallContent(
    content,
    hasAccess
  )

  // Check if content is long (more than 150 characters for timeline)
  const isLongContent = content.length > 150
  const shouldTruncate = isLongContent && !showFullContent
  const displayContent = shouldTruncate ? content.substring(0, 150) + '...' : content

  // If user has access or content is free, show everything
  if (hasAccess || isFree) {
    return (
      <div className="space-y-4">
        {/* Content */}
        <div className="prose prose-gray max-w-none prose-headings:text-gray-900 prose-p:text-gray-800 prose-li:text-gray-800 prose-strong:text-gray-900">
          <div
            dangerouslySetInnerHTML={{ __html: displayContent }}
            className="whitespace-pre-wrap text-gray-800"
          />
          {shouldTruncate && (
            <button
              onClick={() => setShowFullContent(true)}
              className="text-blue-600 hover:text-blue-700 font-medium text-sm mt-2"
            >
              More...
            </button>
          )}
        </div>

        {/* Photos - Centered */}
        {photos.length > 0 && (
          <div className="flex justify-center">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl">
              {photos.map((photo) => (
                <div key={photo.id} className="relative">
                  <Image
                    src={photo.url}
                    alt={photo.alt_text}
                    width={600}
                    height={400}
                    className="rounded-lg object-cover w-full h-auto cursor-pointer hover:opacity-90 transition-opacity"
                    onClick={() => setSelectedPhoto(photo.url)}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Photo Modal */}
        {selectedPhoto && (
          <div
            className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedPhoto(null)}
          >
            <div className="relative max-w-4xl max-h-full">
              <Image
                src={selectedPhoto}
                alt="Full size"
                width={1200}
                height={800}
                className="max-w-full max-h-full object-contain"
              />
              <button
                onClick={() => setSelectedPhoto(null)}
                className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-75"
              >
                ✕
              </button>
            </div>
          </div>
        )}
      </div>
    )
  }

  // Paywall content - show truncated text and blurred photos
  return (
    <>
      <div className="space-y-3">
      {/* Truncated Content */}
      <div className="prose prose-gray max-w-none prose-headings:text-gray-900 prose-p:text-gray-800 prose-li:text-gray-800 prose-strong:text-gray-900">
        <div
          dangerouslySetInnerHTML={{ __html: truncatedContent }}
          className="whitespace-pre-wrap text-gray-800"
        />
        {remainingWords > 0 && (
          <p className="text-gray-500 italic mt-2">
            +{remainingWords} more words...
          </p>
        )}
      </div>

      {/* Blurred Photos - Centered */}
      {photos.length > 0 && (
        <div className="flex justify-center">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl">
            {photos.slice(0, 3).map((photo) => (
              <div key={photo.id} className="relative">
                <Image
                  src={photo.url}
                  alt={photo.alt_text}
                  width={600}
                  height={400}
                  className="rounded-lg object-cover w-full h-auto filter blur-lg"
                />
              <div
                className="absolute inset-0 bg-black bg-opacity-40 rounded-lg flex items-center justify-center cursor-pointer hover:bg-opacity-50 transition-all"
                onClick={handleSubscribe}
              >
                <div className="text-white text-center">
                  <div className="text-3xl mb-2">🔒</div>
                  <p className="text-sm font-medium">Subscribe to View</p>
                  <p className="text-xs opacity-75 mt-1">
                    {subscribing ? 'Loading...' : 'Click to subscribe'}
                  </p>
                </div>
              </div>
            </div>
          ))}
          {photos.length > 3 && (
            <div className="relative bg-gray-200 rounded-lg h-64 flex items-center justify-center">
              <div className="text-center text-gray-600">
                <div className="text-3xl mb-2">📸</div>
                <p className="text-sm font-medium">+{photos.length - 3} more photos</p>
                <p className="text-xs">Subscribe to view all</p>
              </div>
            </div>
          )}
          </div>
        </div>
      )}

      {/* Paywall Call-to-Action - Smaller */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
        <div className="mb-3">
          <h3 className="text-sm font-medium text-gray-900 mb-1">
            {type === 'chapter' ? 'Continue reading this chapter' : `Continue reading ${writerName}'s story`}
          </h3>
          <p className="text-gray-600 text-xs">
            {remainingWords} words remaining • {photos.length} photo{photos.length !== 1 ? 's' : ''} locked
          </p>
        </div>

        <button
          onClick={handleSubscribe}
          disabled={subscribing}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 text-sm font-medium rounded-lg disabled:opacity-50 transition-colors"
        >
            {subscribing ? 'Loading...' : `Subscribe to ${writerName}`}
          </button>
        </div>

        <p className="text-xs text-gray-500 mt-2">
          Cancel anytime • Support authentic storytelling
        </p>
      </div>
      </div>
    </>
  )
}
