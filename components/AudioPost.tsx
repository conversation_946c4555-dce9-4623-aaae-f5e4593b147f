'use client'

import { useState } from 'react'
import Image from 'next/image'
import { AudioPlayer } from './AudioPlayer'
import { Button } from './ui/button'
import { Heart, MessageCircle, MoreHorizontal } from 'lucide-react'

interface AudioPost {
  id: string
  audio_url: string
  description?: string
  duration_seconds: number
  love_count: number
  reply_count: number
  created_at: string
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
  }
}

interface AudioPostProps {
  post: AudioPost
  currentUserId?: string
  isFollowing?: boolean
  autoPlay?: boolean
  onLove?: (postId: string) => void
  onReply?: (postId: string) => void
  onUserClick?: (userId: string) => void
}

export function AudioPost({
  post,
  currentUserId,
  isFollowing = false,
  autoPlay = false,
  onLove,
  onReply,
  onUserClick
}: AudioPostProps) {
  const [isLoved, setIsLoved] = useState(false)
  const [loveCount, setLoveCount] = useState(post.love_count)
  const [showReplies, setShowReplies] = useState(false)

  const handleLove = async () => {
    if (!currentUserId || !onLove) return

    try {
      setIsLoved(!isLoved)
      setLoveCount(prev => isLoved ? prev - 1 : prev + 1)
      await onLove(post.id)
    } catch (error) {
      // Revert on error
      setIsLoved(!isLoved)
      setLoveCount(prev => isLoved ? prev + 1 : prev - 1)
      console.error('Error toggling love:', error)
    }
  }

  const handleReply = () => {
    if (onReply) {
      onReply(post.id)
    }
  }

  const handleUserClick = () => {
    if (onUserClick) {
      onUserClick(post.user.id)
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const postDate = new Date(dateString)
    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'now'
    if (diffInMinutes < 60) return `${diffInMinutes}m`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`
    return `${Math.floor(diffInMinutes / 1440)}d`
  }

  return (
    <div className="bg-white rounded-2xl p-4 sm:p-6 shadow-sm border border-gray-100">
      {/* Header */}
      <div className="flex items-start gap-3 mb-4">
        <button
          onClick={handleUserClick}
          className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0 hover:ring-2 hover:ring-gray-300 transition-all cursor-pointer"
        >
          {post.user.avatar || post.user.profile_picture_url ? (
            <Image
              src={(post.user.avatar || post.user.profile_picture_url) as string}
              alt={post.user.name || 'User avatar'}
              width={48}
              height={48}
              className="w-full h-full object-cover"
            />
          ) : (
            <span className="text-gray-500 text-lg">👤</span>
          )}
        </button>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <button
              onClick={handleUserClick}
              className="font-medium text-gray-900 hover:text-gray-700 transition-colors truncate"
            >
              {post.user.name}
            </button>
            <span className="text-gray-400 text-sm">•</span>
            <span className="text-gray-500 text-sm">
              {formatTimeAgo(post.created_at)}
            </span>
          </div>
          
          {/* Audio indicator */}
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <span className="text-blue-500">🎵</span>
            <span>Audio • {post.duration_seconds.toFixed(1)}s</span>
            {isFollowing && (
              <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full">
                Following
              </span>
            )}
          </div>
        </div>

        <Button variant="ghost" size="sm" className="p-2">
          <MoreHorizontal className="w-4 h-4" />
        </Button>
      </div>

      {/* Description */}
      {post.description && (
        <div className="mb-4">
          <p className="text-gray-800 text-sm sm:text-base">
            {post.description}
          </p>
        </div>
      )}

      {/* Audio Player */}
      <div className="mb-4">
        <AudioPlayer
          audioUrl={post.audio_url}
          duration={post.duration_seconds}
          autoPlay={autoPlay && isFollowing}
          className="w-full"
        />
      </div>

      {/* Actions */}
      <div className="flex items-center gap-4">
        <button
          onClick={handleLove}
          disabled={!currentUserId}
          className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all ${
            isLoved
              ? 'bg-red-50 text-red-600'
              : 'text-gray-600 hover:bg-gray-50'
          } ${!currentUserId ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <Heart 
            className={`w-4 h-4 ${isLoved ? 'fill-current' : ''}`} 
          />
          <span className="text-sm font-medium">{loveCount}</span>
        </button>

        <button
          onClick={handleReply}
          disabled={!currentUserId}
          className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all text-gray-600 hover:bg-gray-50 ${
            !currentUserId ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          <MessageCircle className="w-4 h-4" />
          <span className="text-sm font-medium">{post.reply_count}</span>
        </button>

        {post.reply_count > 0 && (
          <button
            onClick={() => setShowReplies(!showReplies)}
            className="text-sm text-blue-600 hover:text-blue-700 font-medium"
          >
            {showReplies ? 'Hide' : 'Show'} replies
          </button>
        )}
      </div>

      {/* Replies section (placeholder for now) */}
      {showReplies && post.reply_count > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <p className="text-sm text-gray-500 text-center">
            Audio replies coming soon...
          </p>
        </div>
      )}
    </div>
  )
}
