'use client'

import { useState } from 'react'
import Image from 'next/image'
import { AudioPlayer } from './AudioPlayer'
import { AudioRecorder } from './AudioRecorder'
import { AudioRepliesList } from './AudioRepliesList'
import { Button } from './ui/button'
import { Heart, MessageCircle, MoreHorizontal } from 'lucide-react'

interface AudioPost {
  id: string
  audio_url: string
  description?: string
  duration_seconds: number
  love_count: number
  reply_count: number
  created_at: string
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
  }
}

interface AudioPostProps {
  post: AudioPost
  currentUserId?: string
  isFollowing?: boolean

  onLove?: (postId: string) => void
  onReply?: (postId: string) => void
  onUserClick?: (userId: string) => void
}

export function AudioPost({
  post,
  currentUserId,
  isFollowing = false,

  onLove,
  onReply,
  onUserClick
}: AudioPostProps) {
  const [isLoved, setIsLoved] = useState(false)
  const [loveCount, setLoveCount] = useState(post.love_count)
  const [showReplies, setShowReplies] = useState(false)
  const [showReplyRecorder, setShowReplyRecorder] = useState(false)

  const handleLove = async () => {
    if (!currentUserId || !onLove) return

    try {
      setIsLoved(!isLoved)
      setLoveCount(prev => isLoved ? prev - 1 : prev + 1)
      await onLove(post.id)
    } catch (error) {
      // Revert on error
      setIsLoved(!isLoved)
      setLoveCount(prev => isLoved ? prev + 1 : prev - 1)
      console.error('Error toggling love:', error)
    }
  }

  const handleReply = () => {
    setShowReplyRecorder(true)
  }

  const handleAudioReplyComplete = async (audioBlob: Blob, duration: number) => {
    try {
      console.log('Audio reply recorded:', { duration, size: audioBlob.size })

      // Step 1: Get upload URL for the reply
      const uploadResponse = await fetch('/api/audio/replies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          parentPostId: post.id,
          duration: Math.round(duration * 10) / 10
        })
      })

      if (!uploadResponse.ok) {
        throw new Error('Failed to get upload URL for reply')
      }

      const { uploadUrl, key, publicUrl } = await uploadResponse.json()

      // Step 2: Upload the audio file to R2
      const uploadResult = await fetch(uploadUrl, {
        method: 'PUT',
        body: audioBlob,
        headers: {
          'Content-Type': 'audio/webm'
        }
      })

      if (!uploadResult.ok) {
        throw new Error('Failed to upload audio reply')
      }

      // Step 3: Save the reply to the database
      const createResponse = await fetch('/api/audio/replies/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          parentPostId: post.id,
          audioUrl: publicUrl,
          audioKey: key,
          duration: Math.round(duration * 10) / 10,
          description: '' // Could add description input later
        })
      })

      if (!createResponse.ok) {
        throw new Error('Failed to save audio reply')
      }

      // Success!
      setShowReplyRecorder(false)

      // Refresh the page or update the reply count
      window.location.reload() // Simple refresh for now

    } catch (error) {
      console.error('Error posting audio reply:', error)
      alert('Failed to post audio reply. Please try again.')
    }
  }

  const handleUserClick = () => {
    if (onUserClick) {
      onUserClick(post.user.id)
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const postDate = new Date(dateString)
    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'now'
    if (diffInMinutes < 60) return `${diffInMinutes}m`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`
    return `${Math.floor(diffInMinutes / 1440)}d`
  }

  return (
    <div className="bg-white rounded-2xl p-4 sm:p-6 shadow-sm border border-gray-100">
      {/* Header */}
      <div className="flex items-start gap-3 mb-4">
        <button
          onClick={handleUserClick}
          className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0 hover:ring-2 hover:ring-gray-300 transition-all cursor-pointer"
        >
          {post.user.avatar || post.user.profile_picture_url ? (
            <Image
              src={(post.user.avatar || post.user.profile_picture_url) as string}
              alt={post.user.name || 'User avatar'}
              width={48}
              height={48}
              className="w-full h-full object-cover"
            />
          ) : (
            <span className="text-gray-500 text-lg">👤</span>
          )}
        </button>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <button
              onClick={handleUserClick}
              className="font-medium text-gray-900 hover:text-gray-700 transition-colors truncate"
            >
              {post.user.name}
            </button>
            <span className="text-gray-400 text-sm">•</span>
            <span className="text-gray-500 text-sm">
              {formatTimeAgo(post.created_at)}
            </span>
          </div>
          
          {/* Audio indicator */}
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <span className="text-blue-500">🎵</span>
            <span>Audio • {post.duration_seconds.toFixed(1)}s</span>
            {isFollowing && (
              <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full">
                Following
              </span>
            )}
          </div>
        </div>

        <Button variant="ghost" size="sm" className="p-2">
          <MoreHorizontal className="w-4 h-4" />
        </Button>
      </div>

      {/* Description */}
      {post.description && (
        <div className="mb-4">
          <p className="text-gray-800 text-sm sm:text-base">
            {post.description}
          </p>
        </div>
      )}

      {/* Audio Player */}
      <div className="mb-4">
        <AudioPlayer
          audioUrl={post.audio_url}
          duration={post.duration_seconds}

          className="w-full"
        />
      </div>

      {/* Actions */}
      <div className="flex items-center gap-2 sm:gap-4 bg-gray-50 p-2 rounded-lg">
        <span className="text-xs text-gray-500 hidden sm:inline">Actions:</span>
        <button
          onClick={handleLove}
          disabled={!currentUserId}
          className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-lg transition-all min-h-[44px] ${
            isLoved
              ? 'bg-red-50 text-red-600'
              : 'text-gray-600 hover:bg-gray-50'
          } ${!currentUserId ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <Heart
            className={`w-4 h-4 ${isLoved ? 'fill-current' : ''}`}
          />
          <span className="text-xs sm:text-sm font-medium">{loveCount}</span>
        </button>

        <button
          onClick={handleReply}
          disabled={!currentUserId}
          className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-lg transition-all text-blue-600 hover:bg-blue-50 min-h-[44px] ${
            !currentUserId ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          <div className="flex items-center gap-1">
            <span className="text-xs sm:text-sm">🎤</span>
            <MessageCircle className="w-4 h-4" />
          </div>
          <span className="text-xs sm:text-sm font-medium">Reply</span>
          {post.reply_count > 0 && (
            <span className="text-xs bg-blue-100 text-blue-700 px-1 sm:px-2 py-1 rounded-full">
              {post.reply_count}
            </span>
          )}
        </button>

        <button
          onClick={() => setShowReplies(!showReplies)}
          className="text-xs sm:text-sm text-blue-600 hover:text-blue-700 font-medium py-2 px-2 sm:px-3 rounded-lg hover:bg-blue-50 min-h-[44px] flex items-center"
        >
          <span>{showReplies ? 'Hide' : 'View'} replies</span>
          {post.reply_count > 0 && (
            <span className="ml-1 text-xs bg-blue-100 text-blue-700 px-1 sm:px-2 py-1 rounded-full">
              {post.reply_count}
            </span>
          )}
        </button>
      </div>

      {/* Audio Reply Recorder */}
      {showReplyRecorder && (
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-blue-900">🎤 Record Audio Reply</h4>
            <button
              onClick={() => setShowReplyRecorder(false)}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              Cancel
            </button>
          </div>
          <AudioRecorder
            maxDuration={9}
            onRecordingComplete={handleAudioReplyComplete}
            onCancel={() => setShowReplyRecorder(false)}
          />
        </div>
      )}

      {/* Audio Replies List */}
      {showReplies && (
        <AudioRepliesList
          postId={post.id}
          currentUserId={currentUserId}
          onReplyCountChange={(count) => {
            // Update the reply count in the post
            // This will be reflected in the UI
          }}
        />
      )}
    </div>
  )
}
